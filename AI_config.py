#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块配置管理
统一管理所有AI模块的配置参数、API密钥和常量
"""

import os
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class APIConfig:
    """API配置类"""
    base_url: str
    api_key: str
    backup_keys: List[str]
    timeout: int = 30
    max_retries: int = 3

@dataclass
class CacheConfig:
    """缓存配置类"""
    ttl: int  # 缓存生存时间（秒）
    max_size: int = 1000  # 最大缓存条目数
    cleanup_interval: int = 300  # 清理间隔（秒）

class AIConfig:
    """AI模块统一配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        
        # 目录配置
        self.CACHE_DIR = os.getenv("AI_CACHE_DIR", "data/cache/coinglass")
        self.COINGLASS_DATA_DIR = os.getenv("COINGLASS_DATA_DIR", "data/coinglass")
        
        # CoinGlass API配置
        self.COINGLASS_API = APIConfig(
            base_url="https://open-api-v4.coinglass.com/api",
            api_key=os.getenv("COINGLASS_API_KEY", "c40fbbee201d4dfab3a4b62f37f0b610"),
            backup_keys=[],
            timeout=30,
            max_retries=3
        )
        
        # Google Gemini API配置
        self.GEMINI_API = APIConfig(
            base_url="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent",
            api_key=os.getenv("GEMINI_API_KEY", "AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4"),
            backup_keys=[
                os.getenv("GEMINI_API_KEY_1", "AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4"),
                os.getenv("GEMINI_API_KEY_2", "AIzaSyBSllSwrObqvUiXqFG5RUJXB6woZoBSaTk"),
                os.getenv("GEMINI_API_KEY_3", "AIzaSyB1PiY-qfnM8Yj8kctuLhSPW8ckUJYu5-U"),
                os.getenv("GEMINI_API_KEY_4", "AIzaSyB3YP05qliWlJnmL6w07rMRT7BUpl_dM94"),
            ],
            timeout=120,
            max_retries=6
        )
        
        # 缓存配置
        self.CACHE_CONFIG = {
            'ai_analysis': CacheConfig(ttl=300, max_size=100),  # AI分析缓存5分钟
            'kline_data': CacheConfig(ttl=60, max_size=500),    # K线数据缓存1分钟
            'orderbook': CacheConfig(ttl=30, max_size=200),     # 订单薄缓存30秒
            'market_data': CacheConfig(ttl=300, max_size=50),   # 市场数据缓存5分钟
        }
        
        # 技术分析参数配置
        self.TECHNICAL_ANALYSIS = {
            'bollinger_periods': list(range(10, 101, 10)),  # 布林带周期：10-100
            'ma_periods': [5, 10, 15, 20, 25, 30, 50, 100, 200],  # MA周期
            'ema_periods': [5, 10, 12, 15, 20, 26, 30, 50, 100, 200],  # EMA周期
            'rsi_periods': [7, 14, 21, 28],  # RSI周期
            'macd_configs': [
                {'fast': 12, 'slow': 26, 'signal': 9},  # 标准MACD
                {'fast': 5, 'slow': 35, 'signal': 5},   # 快速MACD
                {'fast': 19, 'slow': 39, 'signal': 9}   # 慢速MACD
            ]
        }
        
        # 风险评估阈值
        self.RISK_THRESHOLDS = {
            'rsi_overbought': 80,
            'rsi_oversold': 20,
            'rsi_warning_high': 70,
            'rsi_warning_low': 30,
            'volatility_high': 5.0,  # ATR百分比
            'volatility_medium': 3.0,
            'volume_spike_threshold': 2.0,  # 成交量异常倍数
        }
        
        # 并发控制配置
        self.CONCURRENCY = {
            'max_concurrent_requests': 3,  # 最大并发请求数
            'batch_analysis_semaphore': 3,  # 批量分析信号量
            'api_rate_limit_delay': 1.0,   # API限流延迟（秒）
        }
        
        # 日志配置
        self.LOGGING = {
            'level': os.getenv("AI_LOG_LEVEL", "INFO"),
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': os.getenv("AI_LOG_FILE", "logs/ai_modules.log"),
        }
        
        # 确保必要目录存在
        self._ensure_directories()
        
        logger.info("✅ AI模块配置管理器初始化完成")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.CACHE_DIR,
            self.COINGLASS_DATA_DIR,
            os.path.dirname(self.LOGGING['file_path']) if self.LOGGING['file_path'] else None
        ]
        
        for directory in directories:
            if directory:
                os.makedirs(directory, exist_ok=True)
    
    def get_api_config(self, api_name: str) -> Optional[APIConfig]:
        """获取API配置"""
        api_configs = {
            'coinglass': self.COINGLASS_API,
            'gemini': self.GEMINI_API
        }
        return api_configs.get(api_name.lower())
    
    def get_cache_config(self, cache_type: str) -> Optional[CacheConfig]:
        """获取缓存配置"""
        return self.CACHE_CONFIG.get(cache_type)
    
    def get_technical_params(self, indicator: str) -> Any:
        """获取技术分析参数"""
        return self.TECHNICAL_ANALYSIS.get(indicator)
    
    def get_risk_threshold(self, threshold_name: str) -> Optional[float]:
        """获取风险阈值"""
        return self.RISK_THRESHOLDS.get(threshold_name)
    
    def update_config(self, section: str, key: str, value: Any):
        """动态更新配置"""
        try:
            if hasattr(self, section.upper()):
                config_section = getattr(self, section.upper())
                if isinstance(config_section, dict):
                    config_section[key] = value
                    logger.info(f"配置已更新: {section}.{key} = {value}")
                else:
                    setattr(config_section, key, value)
                    logger.info(f"配置已更新: {section}.{key} = {value}")
            else:
                logger.warning(f"未找到配置节: {section}")
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
    
    def validate_config(self) -> Dict[str, bool]:
        """验证配置完整性"""
        validation_results = {}
        
        # 验证API密钥
        validation_results['coinglass_api_key'] = bool(self.COINGLASS_API.api_key)
        validation_results['gemini_api_key'] = bool(self.GEMINI_API.api_key)
        
        # 验证目录
        validation_results['cache_dir_exists'] = os.path.exists(self.CACHE_DIR)
        validation_results['coinglass_dir_exists'] = os.path.exists(self.COINGLASS_DATA_DIR)
        
        # 验证技术分析参数
        validation_results['technical_params_valid'] = all([
            self.TECHNICAL_ANALYSIS.get('bollinger_periods'),
            self.TECHNICAL_ANALYSIS.get('ma_periods'),
            self.TECHNICAL_ANALYSIS.get('rsi_periods')
        ])
        
        return validation_results

# 全局配置实例
config = AIConfig()

# 便捷访问函数
def get_config() -> AIConfig:
    """获取全局配置实例"""
    return config

def get_api_config(api_name: str) -> Optional[APIConfig]:
    """获取API配置的便捷函数"""
    return config.get_api_config(api_name)

def get_cache_config(cache_type: str) -> Optional[CacheConfig]:
    """获取缓存配置的便捷函数"""
    return config.get_cache_config(cache_type)
